const esbuild = require('esbuild');
const fs = require('fs-extra');
const path = require('path');
const { execSync } = require('child_process');

const AUTHOR = 'Ever Co. LTD <<EMAIL>>';

async function buildCLI() {
    const buildDir = path.join(__dirname, 'dist');
    const tempDir = path.join(__dirname, 'temp-build');
    const packageJsonPath = path.join(__dirname, 'package.json');

    // Clean build directories
    await fs.remove(buildDir);
    await fs.remove(tempDir);
    await fs.ensureDir(buildDir);
    await fs.ensureDir(tempDir);

    console.log('Building agent package...');

    // First, ensure the agent package is built
    try {
        execSync('pnpm --filter "@packages/agent" build', {
            cwd: path.resolve(__dirname, '../..'),
            stdio: 'inherit',
        });
    } catch (error) {
        console.error('Agent package build failed:', error.message);
        throw error;
    }

    console.log('Compiling CLI TypeScript with decorators...');

    // Then, compile CLI TypeScript with proper decorator support
    try {
        execSync(
            'npx tsc --project tsconfig.json --outDir temp-build --emitDecoratorMetadata true --experimentalDecorators true --target ES2020 --module Node16 --moduleResolution Node16 --esModuleInterop true --allowSyntheticDefaultImports true --skipLibCheck true',
            {
                cwd: __dirname,
                stdio: 'inherit',
            },
        );
    } catch (error) {
        console.error('TypeScript compilation failed:', error.message);
        throw error;
    }

    console.log('Bundling with esbuild...');

    // Then bundle the compiled JavaScript
    await esbuild.build({
        entryPoints: ['temp-build/main.js'],
        bundle: true,
        platform: 'node',
        target: 'node20',
        outfile: 'dist/cli.js',
        banner: {
            js: '#!/usr/bin/env node\nprocess.env.NODE_ENV = process.env.NODE_ENV || "production";\nrequire("reflect-metadata");\nrequire("process").removeAllListeners("warning")',
        },
        // External dependencies that should not be bundled
        external: [
            // Native modules that can't be bundled
            'better-sqlite3',
            'libsodium-wrappers',

            // Required for TypeORM decorators
            'reflect-metadata',

            // Optional NestJS modules
            '@nestjs/microservices',
            '@nestjs/websockets/socket-module',

            // Build Dependencies
            'fs-extra',

            // Node.js built-ins (esbuild handles these automatically, but being explicit)
            'fs',
            'path',
            'os',
            'crypto',
            'http',
            'https',
            'url',
            'util',
            'stream',
            'events',
            'buffer',
            'child_process',
            'tty',
            'readline',
        ],
        format: 'cjs',
        minify: true, // Keep readable for debugging
        sourcemap: false,
        metafile: true,
        // Preserve decorator metadata for TypeORM
        keepNames: true,
        // Enable experimental decorators support
        tsconfigRaw: {
            compilerOptions: {
                experimentalDecorators: true,
                emitDecoratorMetadata: true,
                useDefineForClassFields: false,
            },
        },
    });

    // Read the current package.json
    const currentPackageJson = await fs.readJson(packageJsonPath);

    // Create publishable package.json
    const publishablePackageJson = {
        name: '@ever-works/cli',
        version: currentPackageJson.version,
        description: 'Ever Works CLI - Open Directory Builder Platform Command Line Interface',
        author: AUTHOR,
        license: 'UNLICENSED',
        homepage: 'https://ever.works',
        repository: {
            type: 'git',
            url: 'https://github.com/ever-works/ever-works.git',
            directory: 'apps/internal-cli',
        },
        bugs: {
            url: 'https://github.com/ever-works/ever-works/issues',
        },
        keywords: [
            'cli',
            'directory',
            'builder',
            'ever-works',
            'automation',
            'ai',
            'markdown',
            'website-generator',
        ],
        bin: {
            ew: './cli.js',
        },
        main: './cli.js',
        files: ['cli.js', 'README.md', 'LICENSE'],
        engines: {
            node: '>=20.0.0',
        },
        // Only include runtime dependencies that are external
        dependencies: {
            'better-sqlite3': '^11.10.0',
            'libsodium-wrappers': '^0.7.15',
            'reflect-metadata': '^0.2.2',
        },
        // Remove dev dependencies and workspace dependencies
        scripts: {
            postinstall:
                'echo "Ever Works CLI installed successfully! Run \'ew --help\' to get started."',
        },
    };

    // Write the publishable package.json
    await fs.writeJson(path.join(buildDir, 'package.json'), publishablePackageJson, { spaces: 2 });

    // Copy README if it exists
    const readmePath = path.join(__dirname, 'README.md');
    if (await fs.pathExists(readmePath)) {
        await fs.copy(readmePath, path.join(buildDir, 'README.md'));
    }

    // Copy LICENSE if it exists
    const licensePath = path.join(__dirname, '../../LICENSE');
    if (
        (await fs.pathExists(licensePath)) ||
        (await fs.pathExists(licensePath + '.md')) ||
        (await fs.pathExists(licensePath + '.txt'))
    ) {
        await fs.copy(licensePath, path.join(buildDir, 'LICENSE'));
    }

    // Clean up temporary directory
    await fs.remove(tempDir);

    console.log('CLI build completed successfully!');
    console.log(`Output directory: ${buildDir}`);
}

buildCLI().catch((error) => {
    console.error('❌ Build failed:', error);
    process.exit(1);
});
