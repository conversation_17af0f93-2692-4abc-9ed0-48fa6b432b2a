{"name": "ever-works-cli", "version": "0.1.0", "description": "", "main": "index.js", "scripts": {"cli": "cd dist && node dist/cli.js", "build": "tsc --noEmit && node build.js", "build:deps": "pnpm --filter '@packages/*' build", "build:cli": "tsc --noEmit && node build.js", "publish:cli": "npm run build:cli && cd dist && npm publish", "test:cli": "cd dist && npm install && node cli.js --help"}, "keywords": [], "author": {"name": "Ever Co. LTD", "email": "<EMAIL>", "url": "https://ever.co"}, "license": "MIT", "packageManager": "pnpm@10.13.1", "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.8", "@types/node": "^22.16.3", "esbuild": "^0.25.6", "typescript": "^5.8.3"}, "dependencies": {"axios": "^1.10.0", "chalk": "^4.1.2", "commander": "^14.0.0", "dotenv": "^17.2.0", "fs-extra": "^11.3.0", "inquirer": "^12.7.0", "ora": "^5.4.1", "@packages/cli-shared": "workspace:*"}}