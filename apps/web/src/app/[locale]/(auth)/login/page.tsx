import { Suspense } from 'react';
import { LoginClient } from './login-client';
import { getAuthFromCookie } from '@/lib/auth';
import { redirect } from '@/i18n/navigation';
import { getLocale } from 'next-intl/server';
import { ROUTES } from '@/lib/constants';

export default async function LoginPage() {
    const locale = await getLocale();
    const user = await getAuthFromCookie();
    if (user) {
        return redirect({ locale, href: ROUTES.DASHBOARD });
    }

    return (
        <Suspense
            fallback={
                <div className="min-h-screen bg-background dark:bg-background-dark flex items-center justify-center">
                    <div className="animate-pulse">
                        <div className="w-12 h-12 bg-surface-secondary dark:bg-surface-secondary-dark rounded-full"></div>
                    </div>
                </div>
            }
        >
            <LoginClient />
        </Suspense>
    );
}
