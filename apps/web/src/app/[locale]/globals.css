@import 'tailwindcss';

@custom-variant dark (&:where(.dark, .dark *));

@theme {
    --font-sans: var(--font-geist-sans);
    --font-mono: var(--font-geist-mono);

    /* Light mode colors (default) */
    --color-background: #ffffff;
    --color-surface: #f8fafc;
    --color-surface-secondary: #f1f5f9;
    --color-surface-tertiary: #e2e8f0;

    --color-card: #ffffff;
    --color-card-hover: #f8fafc;
    --color-card-border: #e2e8f0;

    --color-primary: #3b82f6;
    --color-primary-hover: #2563eb;

    --color-text: #0f172a;
    --color-text-secondary: #475569;
    --color-text-muted: #94a3b8;

    --color-border: #e2e8f0;
    --color-border-secondary: #cbd5e1;

    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-danger: #ef4444;
    --color-info: #3b82f6;

    /* Dark mode colors */
    --color-background-dark: #0f1419;
    --color-surface-dark: #0f172a;
    --color-surface-secondary-dark: #1e293b;
    --color-surface-tertiary-dark: #334155;

    --color-card-dark: #1e293b;
    --color-card-hover-dark: #334155;
    --color-card-border-dark: #334155;

    --color-text-dark: #e2e8f0;
    --color-text-secondary-dark: #94a3b8;
    --color-text-muted-dark: #64748b;

    --color-border-dark: #334155;
    --color-border-secondary-dark: #475569;
}

html,
body {
    max-width: 100vw;
    overflow-x: hidden;
}

body {
    @apply bg-background text-text dark:bg-background-dark dark:text-text-dark;
    font-family:
        var(--font-geist-sans),
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        Roboto,
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    @apply bg-surface-secondary dark:bg-surface-secondary-dark;
}

::-webkit-scrollbar-thumb {
    @apply bg-border dark:bg-border-dark rounded;
}

::-webkit-scrollbar-thumb:hover {
    @apply bg-border-secondary dark:bg-border-secondary-dark;
}

/* Sonner Toast Overrides - Simple approach */
[data-sonner-toaster] {
    --normal-bg: #f8fafc;
    --normal-text: #0f172a;
    --normal-border: #e2e8f0;
}

[data-sonner-toaster][data-theme='dark'] {
    --normal-bg: #0f172a;
    --normal-text: #e2e8f0;
    --normal-border: #334155;
}

[data-sonner-toast] {
    background: var(--normal-bg) !important;
    color: var(--normal-text) !important;
    border: 1px solid var(--normal-border) !important;
}

[data-sonner-toast] [data-description] {
    color: inherit !important;
    opacity: 0.8;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes pulse-glow {
    0%,
    100% {
        opacity: 0.5;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

@keyframes gradient-shift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes slide-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
    animation: pulse-glow 4s ease-in-out infinite;
}

.animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 10s ease infinite;
}

.animate-slide-up {
    animation: slide-up 0.6s ease-out;
}

/* Glassmorphism */
.glass {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .glass {
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-dark {
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.dark .glass-dark {
    background: rgba(15, 23, 42, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Utility classes */
.line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

/* Gradient text */
.gradient-text {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
