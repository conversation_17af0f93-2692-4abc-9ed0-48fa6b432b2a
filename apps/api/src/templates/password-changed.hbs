<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Ever Works password has been changed</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8fafc;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        .logo-section {
            padding: 40px 20px 30px;
            text-align: center;
        }
        .logo-title {
            font-size: 28px;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
        }
        .logo-subtitle {
            font-size: 14px;
            color: #94a3b8;
            margin: 5px 0 0;
        }
        .main-card {
            margin: 0 20px 40px;
            background-color: #ffffff;
            border-radius: 8px;
            padding: 40px 30px;
        }
        .icon {
            text-align: center;
            font-size: 40px;
            margin-bottom: 20px;
        }
        .title {
            font-size: 24px;
            color: #1e293b;
            font-weight: 600;
            text-align: center;
            margin: 0 0 10px;
        }
        .subtitle {
            text-align: center;
            color: #64748b;
            margin: 0 0 20px;
            font-size: 16px;
        }
        .details-box {
            background-color: #f8fafc;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .details-text {
            font-size: 14px;
            color: #64748b;
            margin: 0;
            line-height: 1.8;
        }
        .security-alert {
            text-align: center;
            font-size: 14px;
            color: #94a3b8;
            margin: 30px 0 0;
        }
        .button {
            display: inline-block;
            text-decoration: none;
            background-color: #ef4444;
            color: #ffffff;
            font-size: 14px;
            font-weight: 500;
            padding: 10px 24px;
            border-radius: 6px;
            text-align: center;
            box-sizing: border-box;
        }
        .button-container {
            text-align: center;
            margin: 20px 0 0;
        }
        .footer {
            padding: 0 20px 40px;
            text-align: center;
        }
        .footer-text {
            font-size: 14px;
            color: #94a3b8;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Logo Section -->
        <div class="logo-section">
            <h1 class="logo-title">Ever Works</h1>
            <p class="logo-subtitle">by Ever Co.</p>
        </div>

        <!-- Main Card -->
        <div class="main-card">
            <!-- Icon -->
            <div class="icon">✅</div>

            <!-- Title -->
            <h2 class="title">Password Changed Successfully</h2>

            <p class="subtitle">
                {{#if firstName}}{{firstName}}, your{{else}}Your{{/if}}
                Ever Works password has been changed successfully.
            </p>

            <!-- Details -->
            <div class="details-box">
                <p class="details-text">
                    <strong>Changed on:</strong>
                    {{changedAt}}<br>
                    <strong>IP Address:</strong>
                    {{ipAddress}}<br>
                    <strong>Location:</strong>
                    {{location}}<br>
                    <strong>Device:</strong>
                    {{device}}{{#if browser}}<br>
                    <strong>Browser:</strong>
                    {{browser}}{{/if}}
                </p>
            </div>

            <!-- Security Alert -->
            <p class="security-alert">
                If you didn't make this change, please secure your account immediately.
            </p>

            <!-- CTA Button -->
            <div class="button-container">
                <a href="{{secureAccountUrl}}" class="button">
                    Secure My Account
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p class="footer-text">
                © 2024 Ever Co. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>