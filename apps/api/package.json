{"name": "ever-works-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"dev": "nest start -b swc --watch", "start": "nest start -b swc", "start:dev": "nest start -b swc --watch", "start:debug": "nest start -b swc --debug --watch", "start:prod": "node dist/main", "build": "nest build -b swc", "build:deps": "pnpm --filter '@packages/*' build"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.1", "@nestjs/common": "^11.1.3", "@nestjs/core": "^11.1.3", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/schedule": "^6.0.0", "@nestjs/throttler": "^6.4.0", "@packages/agent": "workspace:*", "axios": "^1.11.0", "bcrypt": "^6.0.0", "better-sqlite3": "^12.2.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dotenv": "^17.0.1", "handlebars": "^4.7.8", "helmet": "^8.1.0", "mysql2": "^3.14.4", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@swc/cli": "^0.6.0", "@swc/core": "^1.12.9", "@types/bcrypt": "^6.0.0", "@types/express": "^5.0.3", "@types/node": "^22.16.0", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "globals": "^16.3.0", "source-map-support": "^0.5.21", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}