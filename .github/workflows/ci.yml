name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  workflow_dispatch:

jobs:
  lint-and-test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 10.13.1

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build all packages
        run: pnpm build

      - name: Build Internal CLI
        working-directory: apps/internal-cli
        run: pnpm build:cli

      - name: Build External CLI
        working-directory: apps/cli
        run: pnpm build:cli

      - name: Test Internal CLI
        working-directory: apps/internal-cli
        run: pnpm test:cli

      - name: Test External CLI
        working-directory: apps/cli
        run: pnpm test:cli
