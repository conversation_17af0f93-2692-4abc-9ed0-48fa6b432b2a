name: Build and Publish CLIs

on:
  push:
    tags:
      - 'v*'
      - 'cli-v*'
      - 'internal-cli-v*'

  workflow_dispatch:
    inputs:
      publish_internal_cli:
        description: 'Publish internal CLI to npm (private)'
        required: true
        type: boolean
        default: false

      publish_external_cli:
        description: 'Publish external CLI to npm (public)'
        required: true
        type: boolean
        default: false

      version_bump:
        description: 'Version bump type (patch, minor, major)'
        required: false
        type: choice
        options:
          - patch
          - minor
          - major
        default: patch

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 10.13.1

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build packages
        run: pnpm build

      - name: Build Internal CLI
        working-directory: apps/internal-cli
        run: pnpm build:cli
        env:
          API_URL: ${{ secrets.API_URL }}
          WEB_URL: ${{ secrets.WEB_URL }}

      - name: Test Internal CLI build
        working-directory: apps/internal-cli
        run: pnpm test:cli

      - name: Build External CLI
        working-directory: apps/cli
        run: pnpm build:cli
        env:
          API_URL: ${{ secrets.API_URL }}
          WEB_URL: ${{ secrets.WEB_URL }}

      - name: Test External CLI build
        working-directory: apps/cli
        run: pnpm test:cli

      - name: Upload Internal CLI artifact
        uses: actions/upload-artifact@v4
        with:
          name: internal-cli-dist-node-${{ matrix.node-version }}
          path: apps/internal-cli/dist/

      - name: Upload External CLI artifact
        uses: actions/upload-artifact@v4
        with:
          name: external-cli-dist-node-${{ matrix.node-version }}
          path: apps/cli/dist/

  publish-internal-cli:
    needs: build
    runs-on: ubuntu-latest
    if: |
      (github.event_name == 'workflow_dispatch' && github.event.inputs.publish_internal_cli == 'true') ||
      (github.event_name == 'push' && (startsWith(github.ref, 'refs/tags/v') || startsWith(github.ref, 'refs/tags/internal-cli-v')))

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 10.13.1

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          registry-url: 'https://registry.npmjs.org'

      - name: Download Internal CLI artifact
        uses: actions/download-artifact@v4
        with:
          name: internal-cli-dist-node-20.x
          path: apps/internal-cli/dist/

      - name: Configure npm for private package
        run: |
          echo "//registry.npmjs.org/:_authToken=${NODE_AUTH_TOKEN}" >> ~/.npmrc
          npm config set access restricted
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Bump version if requested
        if: github.event_name == 'workflow_dispatch' && github.event.inputs.version_bump
        working-directory: apps/internal-cli
        run: |
          npm version ${{ github.event.inputs.version_bump }} --no-git-tag-version
          # Update the version in the built package.json
          node -e "
          const fs = require('fs');
          const pkg = JSON.parse(fs.readFileSync('package.json'));
          const distPkg = JSON.parse(fs.readFileSync('dist/package.json'));
          distPkg.version = pkg.version;
          fs.writeFileSync('dist/package.json', JSON.stringify(distPkg, null, 2));
          "

      - name: Publish to npm (private)
        working-directory: apps/internal-cli/dist
        run: npm publish --access restricted
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

  publish-external-cli:
    needs: build
    runs-on: ubuntu-latest
    if: |
      (github.event_name == 'workflow_dispatch' && github.event.inputs.publish_external_cli == 'true') ||
      (github.event_name == 'push' && (startsWith(github.ref, 'refs/tags/v') || startsWith(github.ref, 'refs/tags/cli-v')))

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 10.13.1

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          registry-url: 'https://registry.npmjs.org'

      - name: Download External CLI artifact
        uses: actions/download-artifact@v4
        with:
          name: external-cli-dist-node-20.x
          path: apps/cli/dist/

      - name: Configure npm
        run: echo "//registry.npmjs.org/:_authToken=${NODE_AUTH_TOKEN}" >> ~/.npmrc
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Bump version if requested
        if: github.event_name == 'workflow_dispatch' && github.event.inputs.version_bump
        working-directory: apps/cli
        run: |
          npm version ${{ github.event.inputs.version_bump }} --no-git-tag-version
          # Update the version in the built package.json
          node -e "
          const fs = require('fs');
          const pkg = JSON.parse(fs.readFileSync('package.json'));
          const distPkg = JSON.parse(fs.readFileSync('dist/package.json'));
          distPkg.version = pkg.version;
          fs.writeFileSync('dist/package.json', JSON.stringify(distPkg, null, 2));
          "

      - name: Publish to npm (public)
        working-directory: apps/cli/dist
        run: npm publish --access public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

  create-release:
    needs: [publish-internal-cli, publish-external-cli]
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/')

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Extract version
        id: version
        run: |
          INTERNAL_VERSION=$(node -p "require('./apps/internal-cli/package.json').version")
          EXTERNAL_VERSION=$(node -p "require('./apps/cli/package.json').version")
          echo "internal_version=$INTERNAL_VERSION" >> $GITHUB_OUTPUT
          echo "external_version=$EXTERNAL_VERSION" >> $GITHUB_OUTPUT

      - name: Create GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref_name }}
          release_name: CLI Release ${{ github.ref_name }}
          body: |
            ## Ever Works CLI Release

            ### Published Packages:
            - **External CLI (public)**: `ever-works-cli@${{ steps.version.outputs.external_version }}`
            - **Internal CLI (private)**: `@ever-works/cli@${{ steps.version.outputs.internal_version }}`

            ### Installation:

            **External CLI (API-based)**:
            ```bash
            npm install -g ever-works-cli
            # or
            pnpm install -g ever-works-cli
            ```

            **Internal CLI (Direct access)**:
            ```bash
            npm install -g @ever-works/cli
            # or  
            pnpm install -g @ever-works/cli
            ```

            ### Changes:
            See [commit history](https://github.com/ever-works/ever-works/commits/main) for details.
          draft: false
          prerelease: false
