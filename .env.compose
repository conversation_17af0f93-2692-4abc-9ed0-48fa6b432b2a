# SHARED CONFIGURATION
WEB_APP_URL=http://localhost:3000

# =============================================================================
# WEB APP CONFIGURATION
# =============================================================================

# AUTH SECRET
AUTH_SECRET=secure-cookie-secret-here

# WEB APP
NEXT_PUBLIC_WEB_URL=${WEB_APP_URL}

# SERVER API URL
API_URL=http://ever-works-api:3100

# ALLOWED REDIRECT HOSTS (comma-separated)
# Useful for authenticating external tools
# hosts listed here will be redirected back with a session token. 
ALLOWED_REDIRECT_URLS=localhost,127.0.0.1


# =============================================================================
# API APP CONFIGURATION
# =============================================================================

# App type: cli | api | test
APP_TYPE=api

# API Port
PORT=3100

# Debugging
DEBUG=false

# WEB App URL
WEB_APP_URL=${WEB_APP_URL}

# CORS Origins (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# ======================================
# JWT CONFIGURATION
# ======================================

JWT_SECRET=secret_key_here

# Access token expiration (e.g., '15m', '1h', '1d' or 'never' to disable)
JWT_ACCESS_TOKEN_EXPIRATION=7d

# Refresh token expiration in days (e.g., '7', '30' or 'never' to disable)
JWT_REFRESH_TOKEN_EXPIRATION_DAYS=14

# Disable all token expiration (useful for development)
# JWT_DISABLE_EXPIRATION=true


# ======================================
# AUTHENTICATION CONFIGURATION
# ======================================

# GitHub Auth
GH_CLIENT_ID=
GH_CLIENT_SECRET=
GH_CALLBACK_URL="${WEB_APP_URL}/api/auth/github/callback"

# Google Auth
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_CALLBACK_URL="${WEB_APP_URL}/api/auth/google/callback"



# ======================================
# GitHub
# ======================================
GH_APIKEY=''
GH_OWNER=''


# ======================================
# Git configuration (used for commits)
# ======================================
GIT_NAME=''
GIT_EMAIL=''


# ======================================
# Vercel
# ======================================
VERCEL_TOKEN=



# ======================================
# AI CONFIGURATION
# ======================================

AI_DEFAULT_PROVIDER='openai'
AI_FALLBACK_PROVIDERS='google,anthropic'

# OpenAI
OPENAI_API_KEY=
OPENAI_MODEL='gpt-4.1'
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=8192

# OpenRouter
OPENROUTER_API_KEY=
OPENROUTER_MODEL='openai/gpt-4.1'
OPENROUTER_TEMPERATURE=0.7
OPENROUTER_MAX_TOKENS=8192

# Google AI (Gemini)
GOOGLE_API_KEY=
GOOGLE_MODEL='gemini-2.5-flash'
GOOGLE_TEMPERATURE=0.7
GOOGLE_MAX_TOKENS=8192

# Anthropic (Claude)
ANTHROPIC_API_KEY=
ANTHROPIC_MODEL='claude-3-5-sonnet-20241022'
ANTHROPIC_TEMPERATURE=0.7
ANTHROPIC_MAX_TOKENS=8192

# Mistral AI
MISTRAL_API_KEY=
MISTRAL_MODEL='mistral-large-latest'
MISTRAL_TEMPERATURE=0.7
MISTRAL_MAX_TOKENS=8192

# Groq
GROQ_API_KEY=
GROQ_MODEL='llama-3.1-70b-versatile'
GROQ_TEMPERATURE=0.7
GROQ_MAX_TOKENS=8192

# DeepSeek
DEEPSEEK_API_KEY=
DEEPSEEK_MODEL='deepseek-chat'
DEEPSEEK_TEMPERATURE=0.7
DEEPSEEK_MAX_TOKENS=8192
DEEPSEEK_BASE_URL='https://api.deepseek.com'

# Ollama
OLLAMA_API_KEY=
OLLAMA_MODEL='llama2'
OLLAMA_TEMPERATURE=0.7
OLLAMA_MAX_TOKENS=8192
OLLAMA_BASE_URL='http://localhost:11434/v1'

# ======================================
# SEARCH AND CONTENT EXTRACTION CONFIGURATION
# ======================================

# Tavily
TAVILY_API_KEY=

# SEARCH SERVICE CONFIGURATION
# Options: 'tavily', 'naive'
EXTRACT_CONTENT_SERVICE='tavily'

# WEB SEARCH SERVICE CONFIGURATION
# Options: 'tavily', 'google-sr'
WEB_SEARCH_SERVICE='tavily'

# ======================================
# DATABASE CONFIGURATION
# ======================================

# Database type: sqlite | postgres | mysql
DATABASE_TYPE=sqlite

# ======================================
# SQLITE CONFIGURATION (when DATABASE_TYPE=sqlite)
# ======================================

# SQLite database path (optional - uses defaults if not set)
# API apps default: :memory: (development) or /tmp/ever-works-api.db (production)
DATABASE_PATH=./data/database.db

# Force in-memory database (true/false) - overrides default behavior
# DATABASE_IN_MEMORY=true


# ======================================
# POSTGRESQL CONFIGURATION (when DATABASE_TYPE=postgres)
# ======================================

# DATABASE_HOST=localhost
# DATABASE_PORT=5432
# DATABASE_USERNAME=postgres
# DATABASE_PASSWORD=your_password
# DATABASE_NAME=ever_works

# ======================================
# MYSQL/MARIADB CONFIGURATION (when DATABASE_TYPE=mysql)
# ======================================

# DATABASE_HOST=localhost
# DATABASE_PORT=3306
# DATABASE_USERNAME=root
# DATABASE_PASSWORD=your_password
# DATABASE_NAME=ever_works

# Enable SSL/TLS for database connections (true/false)
DATABASE_CA_CERT=
DATABASE_SSL_MODE=false

# ======================================
# COMMON DATABASE OPTIONS
# ======================================

# Enable SQL query logging (true/false)
DATABASE_LOGGING=false

# ======================================
# EMAIL CONFIGURATION
# ======================================

# MAILER PROVIDER: smtp|none
MAILER_PROVIDER=none

# Email from
EMAIL_FROM='Ever Works <<EMAIL>>'

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_password
SMTP_SECURE=false
SMTP_IGNORE_TLS=false
